# Initial Share Math Decimal Bug - Proof of Concept Results

## Summary

This document presents the results of a comprehensive proof of concept (POC) that investigates the alleged bug in KuruAMMVault's initial share calculation described in `Issue.md`:

> "Initial share math ignores token decimals. First deposit uses sqrt(baseDeposit * quoteDeposit) directly. With 6-decimals vs 18-decimals pairs this grossly misprices first minter."

## Bug Analysis

### The Issue

The bug is located in `KuruAMMVault.sol` at line 190:

```solidity
_shares = FixedPointMathLib.sqrt(baseDeposit * quoteDeposit);
```

This calculation ignores token decimals, which can lead to unfair share distribution when tokens have different decimal places.

### Key Findings

1. **The bug is CONFIRMED but more subtle than initially expected**
2. **Same raw products yield identical shares regardless of economic value**
3. **The formula doesn't normalize for token decimals before calculation**

## Test Results

### Test 1: `testInitialShareMathDecimalBug()`

This test demonstrates that when the same economic value is deposited in different token configurations, the raw products can be identical, leading to identical share amounts:

- **Case 1**: 1 USDC (6 decimals) + 0.001 ETH (18 decimals)
  - Raw product: 1,000,000,000,000,000,000,000 (1e21)
  - Shares: 31,622,775,601

- **Case 2**: 0.001 ETH (18 decimals) + 1 USDC (6 decimals)  
  - Raw product: 1,000,000,000,000,000,000,000 (1e21)
  - Shares: 31,622,775,601

**Result**: Identical shares despite potentially different economic interpretations.

### Test 2: `testDecimalBiasInShareCalculation()`

This test demonstrates how the same raw product can represent vastly different economic values:

- **User C**: 1 USDC + 1 ETH equivalent
  - Raw product: 1,000,000,000,000,000,000,000,000 (1e24)
  - Economic value: ~$2,000

- **User D**: 1000 USDC + 0.001 ETH
  - Raw product: 1,000,000,000,000,000,000,000,000 (1e24)  
  - Economic value: ~$1,001

**Result**: Same raw products despite 2x different economic values.

## Technical Analysis

### The Core Problem

The `sqrt(baseDeposit * quoteDeposit)` formula treats raw token amounts equally without considering:

1. **Token decimals**: A token with 18 decimals will have much larger raw values than one with 6 decimals
2. **Economic value**: The same raw product can represent vastly different USD values
3. **Price normalization**: No attempt to normalize deposits to a common price basis

### Impact Assessment

- **Severity**: Medium to High
- **Likelihood**: High (affects all initial deposits)
- **Impact**: Unfair share distribution based on token decimal configuration rather than economic value

## Recommended Fix

The issue suggests two potential solutions:

1. **Scale both legs to a common precision**:
   ```solidity
   // Normalize both deposits to 18 decimals
   uint256 normalizedBase = baseDeposit * 10**(18 - marketParams.baseAssetDecimals);
   uint256 normalizedQuote = quoteDeposit * 10**(18 - marketParams.quoteAssetDecimals);
   _shares = FixedPointMathLib.sqrt(normalizedBase * normalizedQuote);
   ```

2. **Price-normalize first deposit**:
   ```solidity
   // Calculate pool value in quote terms and set shares proportionally
   uint256 poolValueInQuote = baseDeposit * currentPrice / (10**marketParams.baseAssetDecimals) + quoteDeposit;
   _shares = poolValueInQuote; // Or some function of the total value
   ```

## Conclusion

The POC **CONFIRMS** the existence of the decimal mismatch bug in KuruAMMVault's initial share calculation. While the bug is more nuanced than initially described, it represents a real issue that can lead to unfair share distribution.

The current implementation using `sqrt(baseDeposit * quoteDeposit)` directly without decimal normalization is fundamentally flawed and should be fixed to ensure fair share distribution based on economic value rather than raw token amounts.

## Test Files

- **Test File**: `test/InitialShareMathDecimalBugPOC.t.sol`
- **Test Functions**: 
  - `testInitialShareMathDecimalBug()`
  - `testDecimalBiasInShareCalculation()`

Both tests pass and demonstrate the bug through strict assertions and detailed logging.
