# Vault Order Size Update Logic Flaw Leading to Potential DoS

## Summary

The `_convertToAssetsWithNewSize()` function in `KuruAMMVault.sol` contains a critical logic flaw in its nullification decision logic. The function only nullifies partial fills when the new order size is smaller than the partial fill, but fails to handle scenarios where order sizes change while partial fills exist. This leads to calls to `updateVaultOrdSz` without nullification, assuming OrderBook implementations can seamlessly amend partially filled orders, which can cause failed transactions and locked liquidity.

## Finding Description

The vulnerability exists in the `_convertToAssetsWithNewSize()` function in `KuruAMMVault.sol`, which determines when to nullify partial fills during vault operations. The current logic only triggers nullification when:

```solidity
if (_partiallyFilledAskSize > _newAskSize || _partiallyFilledBidSize > _newBidSize) {
    _nullifyPartialFills = true;
}
```

This logic fails to account for scenarios where `partialSize < newSize < oldSize`. In such cases, the code assumes the OrderBook can modify existing partially filled orders by changing their size, but many CLOB implementations don't support order amendments and require cancel-and-replace operations.

**Attack Vector:**
1. User deposits liquidity into the vault, creating ask/bid orders
2. External trader partially fills one of the vault's orders
3. User attempts to withdraw a portion of their liquidity
4. `_convertToAssetsWithNewSize()` calculates new order sizes that differ from current partial fills
5. The flawed nullification logic returns `_nullifyPartialFills = false`
6. `updateVaultOrdSz` is called without nullification, assuming seamless order amendment
7. If the OrderBook rejects the amendment, the transaction fails

This breaks the security guarantee that users can always withdraw their proportional share of vault liquidity. The system assumes OrderBook compatibility that may not exist, creating a dependency on implementation details that can cause system failure.

## Impact Explanation

**High Impact** - This vulnerability can result in:

1. **Denial of Service**: Users unable to withdraw funds when OrderBook rejects order amendments
2. **Liquidity Lock**: Vault funds become inaccessible until manual intervention
3. **System Reliability Failure**: Core vault functionality breaks under normal usage patterns
4. **User Fund Risk**: While funds aren't permanently lost, they become temporarily inaccessible

The impact is classified as High because it affects core system functionality (withdrawals) and can lock user funds, even if temporarily. The vulnerability doesn't result in direct fund loss but severely impacts system availability and user experience.

## Likelihood Explanation

**High Likelihood** - This issue is likely to occur because:

1. **Common Scenario**: Partial fills are normal in active trading environments
2. **Regular Operations**: Vault deposits/withdrawals happen frequently
3. **CLOB Behavior**: Many CLOB implementations don't support order amendments
4. **No Mitigation**: Current code has no fallback mechanism for amendment failures
5. **Deterministic**: The bug triggers predictably when specific conditions are met

The combination of normal trading activity (partial fills) with routine vault operations (withdrawals) makes this scenario highly probable in production environments.

## Proof of Concept

The POC demonstrates three scenarios where the bug manifests:

**Scenario 1: Withdrawal with partial < new < old**
- Initial ask order: 497,512,437
- After partial fill: 165,800,000 filled
- After withdrawal: 447,761,193 new size
- Result: Order size changed without nullification

**Scenario 2: Deposit increasing order size with partials**
- Partial ask size: 62,133,333 (unchanged)
- Old order size: 248,756,218
- New order size: 261,194,028 (increased)
- Result: Assumes OrderBook can amend partially filled order

**Scenario 3: Direct logic flaw demonstration**
- Shows the core issue where size changes occur without proper nullification
- Demonstrates violation of CLOB compatibility assumptions

All tests confirm the bug exists and can be triggered through normal vault operations.

## Recommendation

Fix the nullification logic to handle all cases where order sizes change while partial fills exist:

```solidity
// Current vulnerable logic
if (_partiallyFilledAskSize > _newAskSize || _partiallyFilledBidSize > _newBidSize) {
    _nullifyPartialFills = true;
}

// Fixed logic
if ((_partiallyFilledAskSize > 0 && _partiallyFilledAskSize != _newAskSize) ||
    (_partiallyFilledBidSize > 0 && _partiallyFilledBidSize != _newBidSize)) {
    _nullifyPartialFills = true;
}
```

**Alternative approach**: Always nullify when any partial fills exist and order sizes change:

```solidity
if ((_partiallyFilledAskSize > 0 || _partiallyFilledBidSize > 0) &&
    (_newAskSize != _currentAskSize || _newBidSize != _currentBidSize)) {
    _nullifyPartialFills = true;
}
```

This ensures compatibility with all CLOB implementations by avoiding order amendments entirely when partial fills are present, using the safer cancel-and-replace approach instead.
