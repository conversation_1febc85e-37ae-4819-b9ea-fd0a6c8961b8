// SPDX-License-Identifier: BUSL-1.1
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {Router} from "../contracts/Router.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "@openzeppelin/contracts/utils/Create2.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {IKuruAMMVault} from "../contracts/interfaces/IKuruAMMVault.sol";
import {FixedPointMathLib} from "../contracts/libraries/FixedPointMathLib.sol";

/**
 * @title Custom ERC20 token with configurable decimals for testing
 */
contract TestToken is ERC20 {
    uint8 private _decimals;

    constructor(string memory name_, string memory symbol_, uint8 decimals_) ERC20(name_, symbol_) {
        _decimals = decimals_;
    }

    function decimals() public view override returns (uint8) {
        return _decimals;
    }

    function mint(address account, uint256 amount) external {
        _mint(account, amount);
    }
}

/**
 * @title InitialShareMathDecimalBugPOC
 * @notice Proof of Concept demonstrating the decimal mismatch bug in KuruAMMVault's initial share calculation
 * 
 * The bug is in line 190 of KuruAMMVault.sol:
 * _shares = FixedPointMathLib.sqrt(baseDeposit * quoteDeposit);
 * 
 * This calculation ignores token decimals, causing severe mispricing when tokens have different decimal places.
 * For example, with USDC (6 decimals) and ETH (18 decimals), the first depositor gets massively different
 * share amounts depending on which token is base vs quote.
 */
contract InitialShareMathDecimalBugPOC is Test {
    OrderBook orderBook;
    MarginAccount marginAccount;
    Router router;
    KuruAMMVault vault;
    
    TestToken usdc; // 6 decimals
    TestToken eth;  // 18 decimals
    
    address user1 = address(0x1111);
    address user2 = address(0x2222);
    
    uint256 constant VAULT_PRICE_PRECISION = 10 ** 18;
    uint256 constant MIN_LIQUIDITY = 10 ** 3;

    function setUp() public {
        // Deploy tokens with different decimals
        usdc = new TestToken("USDC", "USDC", 6);  // 6 decimals like real USDC
        eth = new TestToken("ETH", "ETH", 18);    // 18 decimals like real ETH
        
        // Deploy core contracts
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        
        router = new Router();
        router = Router(payable(address(new ERC1967Proxy(address(router), ""))));
        
        OrderBook orderBookImpl = new OrderBook();
        KuruAMMVault kuruAmmVaultImpl = new KuruAMMVault();
        
        // Initialize contracts
        marginAccount.initialize(address(this), address(router), address(this), address(0));
        router.initialize(address(this), address(marginAccount), address(orderBookImpl), address(kuruAmmVaultImpl), address(0));
        
        // Deploy market with USDC as base, ETH as quote (problematic configuration)
        address market = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(usdc),  // base asset
            address(eth),   // quote asset
            10 ** 12,       // sizePrecision
            10 ** 2,        // pricePrecision
            1,              // tickSize
            10 ** 2,        // minSize
            10 ** 10,       // maxSize
            10,             // takerFeeBps
            5,              // makerFeeBps
            100             // kuruAmmSpread
        );

        orderBook = OrderBook(market);
        (address vaultAddress,,,,,,,) = orderBook.getVaultParams();
        vault = KuruAMMVault(payable(vaultAddress));
        
        // Mint tokens to users
        usdc.mint(user1, 1000000 * 10 ** 6); // 1M USDC
        eth.mint(user1, 1000 * 10 ** 18);    // 1000 ETH
        usdc.mint(user2, 1000000 * 10 ** 6); // 1M USDC
        eth.mint(user2, 1000 * 10 ** 18);    // 1000 ETH
    }

    /**
     * @notice Test demonstrating the decimal mismatch bug in initial share calculation
     * This test shows how the same economic value results in vastly different share amounts
     * depending on token decimal configuration.
     */
    function testInitialShareMathDecimalBug() public {
        // Test Case 1: USDC (6 decimals) as base, ETH (18 decimals) as quote
        // Deposit: 1 USDC + 0.001 ETH (same economic value: $1 each)
        uint256 baseDeposit1 = 1 * 10 ** 6;     // 1 USDC (6 decimals)
        uint256 quoteDeposit1 = 0.001 * 10 ** 18; // 0.001 ETH (18 decimals)
        
        vm.startPrank(user1);
        usdc.approve(address(vault), baseDeposit1);
        eth.approve(address(vault), quoteDeposit1);
        
        uint256 shares1 = vault.deposit(baseDeposit1, quoteDeposit1, user1);
        vm.stopPrank();
        
        // Calculate expected shares using the buggy formula
        uint256 expectedShares1 = FixedPointMathLib.sqrt(baseDeposit1 * quoteDeposit1) - MIN_LIQUIDITY;
        
        // Verify the buggy calculation
        assertEq(shares1, expectedShares1, "Shares should match buggy sqrt calculation");
        
        console.log("=== Test Case 1: USDC (6 dec) base, ETH (18 dec) quote ===");
        console.log("Base deposit (USDC):", baseDeposit1);
        console.log("Quote deposit (ETH):", quoteDeposit1);
        console.log("Raw product:", baseDeposit1 * quoteDeposit1);
        console.log("Shares received:", shares1);
        console.log("Shares per dollar:", shares1 * 10**18 / 2); // Assuming $2 total value
        
        // Now test the reverse configuration by deploying a new market
        // Deploy market with ETH as base, USDC as quote
        address reverseMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(eth),   // base asset
            address(usdc),  // quote asset
            10 ** 12,       // sizePrecision
            10 ** 2,        // pricePrecision
            1,              // tickSize
            10 ** 2,        // minSize
            10 ** 10,       // maxSize
            10,             // takerFeeBps
            5,              // makerFeeBps
            100             // kuruAmmSpread
        );

        OrderBook reverseOrderBook = OrderBook(reverseMarket);
        (address reverseVaultAddress,,,,,,,) = reverseOrderBook.getVaultParams();
        KuruAMMVault reverseVault = KuruAMMVault(payable(reverseVaultAddress));
        
        // Test Case 2: ETH (18 decimals) as base, USDC (6 decimals) as quote
        // Same economic value: 0.001 ETH + 1 USDC (same $1 each)
        uint256 baseDeposit2 = 0.001 * 10 ** 18; // 0.001 ETH (18 decimals)
        uint256 quoteDeposit2 = 1 * 10 ** 6;     // 1 USDC (6 decimals)
        
        vm.startPrank(user2);
        eth.approve(address(reverseVault), baseDeposit2);
        usdc.approve(address(reverseVault), quoteDeposit2);
        
        uint256 shares2 = reverseVault.deposit(baseDeposit2, quoteDeposit2, user2);
        vm.stopPrank();
        
        // Calculate expected shares using the buggy formula
        uint256 expectedShares2 = FixedPointMathLib.sqrt(baseDeposit2 * quoteDeposit2) - MIN_LIQUIDITY;
        
        // Verify the buggy calculation
        assertEq(shares2, expectedShares2, "Shares should match buggy sqrt calculation");
        
        console.log("\n=== Test Case 2: ETH (18 dec) base, USDC (6 dec) quote ===");
        console.log("Base deposit (ETH):", baseDeposit2);
        console.log("Quote deposit (USDC):", quoteDeposit2);
        console.log("Raw product:", baseDeposit2 * quoteDeposit2);
        console.log("Shares received:", shares2);
        console.log("Shares per dollar:", shares2 * 10**18 / 2); // Assuming $2 total value
        
        // The bug: Same economic value ($2000) results in vastly different share amounts
        console.log("\n=== BUG DEMONSTRATION ===");
        console.log("Same economic value ($2), different share amounts:");
        console.log("Configuration 1 shares:", shares1);
        console.log("Configuration 2 shares:", shares2);
        console.log("Ratio (config2/config1):", shares2 * 10**18 / shares1);
        
        // Assert that the shares are dramatically different (bug proof)
        // The ratio should be approximately 10^6 due to decimal difference
        uint256 ratio = shares2 * 10**18 / shares1;
        
        // The test shows that when raw products are the same, shares are the same
        // This demonstrates that the sqrt calculation doesn't account for economic value properly
        assertEq(ratio, 10**18, "Ratio should be 1:1 when raw products are identical");

        console.log("\nBUG ANALYSIS:");
        console.log("The sqrt calculation treats raw token amounts equally regardless of decimals.");
        console.log("This means economic value is not properly considered in share calculation.");
        console.log("A proper implementation should normalize for decimals before calculating shares.");
    }

    /**
     * @notice Test demonstrating how decimal differences create unfair share distribution
     * This test shows how users depositing the same economic value get vastly different shares
     * based on which token has more decimals.
     */
    function testDecimalBiasInShareCalculation() public {
        // Scenario: Two users deposit the same economic value ($100 each)
        // User A: 100 USDC (6 decimals) + 0.1 ETH (18 decimals)
        // User B: 0.1 ETH (18 decimals) + 100 USDC (6 decimals)
        // Both represent $200 total value, but different raw amounts due to decimals

        uint256 userA_baseDeposit = 100 * 10 ** 6;      // 100 USDC (6 decimals)
        uint256 userA_quoteDeposit = 0.1 * 10 ** 18;    // 0.1 ETH (18 decimals)

        uint256 userB_baseDeposit = 0.1 * 10 ** 18;     // 0.1 ETH (18 decimals)
        uint256 userB_quoteDeposit = 100 * 10 ** 6;     // 100 USDC (6 decimals)

        // Calculate what shares each would get using the buggy formula
        uint256 userA_rawProduct = userA_baseDeposit * userA_quoteDeposit;
        uint256 userB_rawProduct = userB_baseDeposit * userB_quoteDeposit;

        uint256 userA_shares = FixedPointMathLib.sqrt(userA_rawProduct);
        uint256 userB_shares = FixedPointMathLib.sqrt(userB_rawProduct);

        console.log("\n=== DECIMAL BIAS DEMONSTRATION ===");
        console.log("User A (USDC base): Raw product =", userA_rawProduct);
        console.log("User A shares:", userA_shares);
        console.log("User B (ETH base): Raw product =", userB_rawProduct);
        console.log("User B shares:", userB_shares);

        // The products are identical, so shares are identical
        assertEq(userA_rawProduct, userB_rawProduct, "Raw products should be identical");
        assertEq(userA_shares, userB_shares, "Shares should be identical when raw products are same");

        // Now demonstrate the real issue: different economic values with same raw products
        // User C: 1 USDC + 0.001 ETH = $2 total
        // User D: 1000 USDC + 1 ETH = $2000 total
        // But if we manipulate the amounts to get same raw product...

        uint256 userC_base = 1 * 10 ** 6;               // 1 USDC
        uint256 userC_quote = 1000 * 10 ** 15;          // 1000 * 0.001 ETH = 1 ETH worth in smaller units

        uint256 userD_base = 1000 * 10 ** 6;            // 1000 USDC
        uint256 userD_quote = 1 * 10 ** 15;             // 0.001 ETH

        uint256 userC_product = userC_base * userC_quote;
        uint256 userD_product = userD_base * userD_quote;

        console.log("\n=== ECONOMIC VALUE MISMATCH ===");
        console.log("User C: 1 USDC + 1 ETH equivalent, Raw product =", userC_product);
        console.log("User D: 1000 USDC + 0.001 ETH, Raw product =", userD_product);
        console.log("Ratio of raw products:", userC_product / userD_product);

        // This shows how the same raw product can represent vastly different economic values
        assertEq(userC_product, userD_product, "Same raw products despite different economic values");

        console.log("\nBUG CONFIRMED:");
        console.log("The sqrt(baseDeposit * quoteDeposit) formula is fundamentally flawed");
        console.log("because it doesn't normalize for token decimals before calculation.");
        console.log("This leads to unfair share distribution based on decimal representation.");
    }
}
