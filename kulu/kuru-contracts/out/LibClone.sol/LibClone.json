{"abi": [{"type": "error", "name": "DeploymentFailed", "inputs": []}, {"type": "error", "name": "ETHTransferFailed", "inputs": []}, {"type": "error", "name": "SaltDoesNotStartWith", "inputs": []}], "bytecode": {"object": "0x6080806040523460175760399081601c823930815050f35b5f80fdfe5f80fdfea26469706673582212205d944a8946de8577e862845845581a503fa268a72fed7059394c475107e0ebb264736f6c634300081c0033", "sourceMap": "2777:104179:57:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x5f80fdfea26469706673582212205d944a8946de8577e862845845581a503fa268a72fed7059394c475107e0ebb264736f6c634300081c0033", "sourceMap": "2777:104179:57:-:0;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"DeploymentFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ETHTransferFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SaltDoesNotStartWith\",\"type\":\"error\"}],\"devdoc\":{\"author\":\"Solady (https://github.com/vectorized/solady/blob/main/src/utils/LibClone.sol)Minimal proxy by 0age (https://github.com/0age)Clones with immutable args by wighawag, zefram.eth, <PERSON>-mon & <PERSON> (https://github.com/Saw-mon-and-<PERSON>/clones-with-immutable-args)Minimal ERC1967 proxy by jtriley-eth (https://github.com/jtriley-eth/minimum-viable-proxy)\",\"details\":\"Minimal proxy: Although the sw0nt pattern saves 5 gas over the ERC1167 pattern during runtime, it is not supported out-of-the-box on Etherscan. Hence, we choose to use the 0age pattern, which saves 4 gas over the ERC1167 pattern during runtime, and has the smallest bytecode. - Automatically verified on Etherscan.Minimal proxy (PUSH0 variant): This is a new minimal proxy that uses the PUSH0 opcode introduced during Shanghai. It is optimized first for minimal runtime gas, then for minimal bytecode. The PUSH0 clone functions are intentionally postfixed with a jarring \\\"_PUSH0\\\" as many EVM chains may not support the PUSH0 opcode in the early months after Shanghai. Please use with caution. - Automatically verified on Etherscan.Clones with immutable args (CWIA): The implementation of CWIA here is does NOT append the immutable args into the calldata passed into delegatecall. It is simply an ERC1167 minimal proxy with the immutable arguments appended to the back of the runtime bytecode. - Uses the identity precompile (0x4) to copy args during deployment.Minimal ERC1967 proxy: An minimal ERC1967 proxy, intended to be upgraded with UUPS. This is NOT the same as ERC1967Factory's transparent proxy, which includes admin logic. - Automatically verified on Etherscan.Minimal ERC1967 proxy with immutable args: - Uses the identity precompile (0x4) to copy args during deployment. - Automatically verified on Etherscan.ERC1967I proxy: An variant of the minimal ERC1967 proxy, with a special code path that activates if `calldatasize() == 1`. This code path skips the delegatecall and directly returns the `implementation` address. The returned implementation is guaranteed to be valid if the keccak256 of the proxy's code is equal to `ERC1967I_CODE_HASH`.Minimal ERC1967 beacon proxy: A minimal beacon proxy, intended to be upgraded with an upgradable beacon. - Automatically verified on Etherscan.Minimal ERC1967 beacon proxy with immutable args: - Uses the identity precompile (0x4) to copy args during deployment. - Automatically verified on Etherscan.\",\"errors\":{\"DeploymentFailed()\":[{\"details\":\"Unable to deploy the clone.\"}],\"ETHTransferFailed()\":[{\"details\":\"The ETH transfer has failed.\"}],\"SaltDoesNotStartWith()\":[{\"details\":\"The salt must start with either the zero address or `by`.\"}]},\"kind\":\"dev\",\"methods\":{},\"stateVariables\":{\"ERC1967I_CODE_HASH\":{\"details\":\"The keccak256 of the deployed code for the ERC1967I proxy.\"},\"ERC1967_BEACON_PROXY_CODE_HASH\":{\"details\":\"The keccak256 of the deployed code for the ERC1967 beacon proxy.\"},\"ERC1967_CODE_HASH\":{\"details\":\"The keccak256 of the deployed code for the ERC1967 proxy.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Minimal proxy library.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"node_modules/solady/src/utils/LibClone.sol\":\"LibClone\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"node_modules/solady/src/utils/LibClone.sol\":{\"keccak256\":\"0x1abdd517d30317e403a00b81be402bb0245cd2a16211f29b2f779fc60777581b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://38d5e33716b7175dd8957b0f513242473743a57f7aa3da7bda579d97f35b5ba0\",\"dweb:/ipfs/QmaeuiGELLpmRkWXXFA6r9Zv5scRU418bSuPLg8Qj65nGQ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "DeploymentFailed"}, {"inputs": [], "type": "error", "name": "ETHTransferFailed"}, {"inputs": [], "type": "error", "name": "SaltDoesNotStartWith"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"node_modules/solady/src/utils/LibClone.sol": "LibClone"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"node_modules/solady/src/utils/LibClone.sol": {"keccak256": "0x1abdd517d30317e403a00b81be402bb0245cd2a16211f29b2f779fc60777581b", "urls": ["bzz-raw://38d5e33716b7175dd8957b0f513242473743a57f7aa3da7bda579d97f35b5ba0", "dweb:/ipfs/QmaeuiGELLpmRkWXXFA6r9Zv5scRU418bSuPLg8Qj65nGQ"], "license": "MIT"}}, "version": 1}, "id": 57}